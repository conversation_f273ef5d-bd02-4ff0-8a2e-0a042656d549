// 闲鱼采集插件 - 内容脚本
(function() {
    'use strict';
    
    // 检查是否已经添加了按钮，避免重复添加
    if (document.getElementById('goofish-collection-button')) {
        return;
    }
    
    // 创建采集按钮
    function createCollectionButton() {
        const button = document.createElement('button');
        button.id = 'goofish-collection-button';
        button.className = 'collection-button';
        button.textContent = '采集页面';
        button.title = '点击采集当前页面数据';
        
        // 添加点击事件（暂时只显示提示）
        button.addEventListener('click', function() {
            console.log('采集按钮被点击');
            // 这里后续会添加具体的采集功能
            alert('采集功能开发中...');
        });
        
        return button;
    }
    
    // 添加按钮到页面
    function addButtonToPage() {
        const button = createCollectionButton();
        document.body.appendChild(button);
        console.log('闲鱼采集按钮已添加到页面');
    }
    
    // 等待页面加载完成
    function initializePlugin() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', addButtonToPage);
        } else {
            addButtonToPage();
        }
    }
    
    // 检查当前URL是否为闲鱼网站
    function isGoofishSite() {
        return window.location.hostname === 'www.goofish.com';
    }
    
    // 监听页面URL变化（SPA应用）
    function observeUrlChanges() {
        let currentUrl = window.location.href;
        
        const observer = new MutationObserver(function() {
            if (currentUrl !== window.location.href) {
                currentUrl = window.location.href;
                
                // URL变化时重新检查是否需要显示按钮
                const existingButton = document.getElementById('goofish-collection-button');
                if (isGoofishSite() && !existingButton) {
                    setTimeout(addButtonToPage, 1000); // 延迟添加，确保页面加载完成
                }
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // 初始化插件
    if (isGoofishSite()) {
        initializePlugin();
        observeUrlChanges();
    }
    
})();
